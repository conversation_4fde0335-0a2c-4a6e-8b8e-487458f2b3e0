package main

import (
	"fmt"
	levelStore "github.com/real-rm/golevelstore"
)

func main() {
	key1 := "15d621c7-4fd4-4b93-b746-c64bcdd53dde-nw"
	key2 := "f45f2f83-b822-421b-9264-d5c4d542edd3"
	
	hash1 := levelStore.MurmurToInt32(key1)
	hash2 := levelStore.MurmurToInt32(key2)
	
	name1, _ := levelStore.Int32ToBase62(hash1)
	name2, _ := levelStore.Int32ToBase62(hash2)
	
	fmt.Printf("Key1: %s -> Hash: %d -> Name: %s\n", key1, hash1, name1)
	fmt.Printf("Key2: %s -> Hash: %d -> Name: %s\n", key2, hash2, name2)
	
	// Test the actual downloaded file names
	actualHash1 := levelStore.MurmurToInt32("15d621c7-4fd4-4b93-b746-c64bcdd53dde-nw")
	actualHash2 := levelStore.MurmurToInt32("f45f2f83-b822-421b-9264-d5c4d542edd3")
	actualName1, _ := levelStore.Int32ToBase62(actualHash1)
	actualName2, _ := levelStore.Int32ToBase62(actualHash2)
	
	fmt.Printf("Actual1: %s\n", actualName1)
	fmt.Printf("Actual2: %s\n", actualName2)
}
